const { getAllArbiterSchema } = require("../schema/playerSchama");
const { sendResponse, handleError } = require("../utils/apiResponse");
const { Op, Sequelize } = require("sequelize");
const { v4: UUIDV4 } = require("uuid");
const {
  ArbiterDetails,
  Pairing,
  User,
  Otp,
  Tournament,
  InviteRequest,
  Notifications,
} = require("../config/db").models;
const {
  arbiterDetailSchema,
  updateDetailsSchema,
} = require("../schema/arbiterSchema");

const { sequelize } = require("../config/db");
const { deleteFromS3 } = require("../utils/s3");
const { config } = require("../config/config");
const user = require("../models/user");
const notificationService = require("../services/notificationService");
const cronService = require("../services/cronService");
const { exportToExcel, exportToPDF } = require("../utils/report-generation");
const emailService = require("../utils/mailer/emailService");

const getAllArbiter = async (req, res) => {
  const { data, success, error } = getAllArbiterSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });      
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      arbiterId,
      arbiterName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (arbiterId && !arbiterId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${arbiterId}%` } },
        { aicfId: { [Op.iLike]: `%${arbiterId}%` } },
        { officialId: { [Op.iLike]: `%${arbiterId}%` } },
      ];
    }
    let userWhereClause = {};
    if (arbiterId && arbiterId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${arbiterId}%` } };
    }
    if (arbiterName) userWhereClause.name = { [Op.iLike]: `%${arbiterName}%` };
    const { rows: arbiters, count } = await ArbiterDetails.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "title",

        "fideId",
        "aicfId",
        "profileUrl",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          as: "user",
          attributes: ["cbid", "name"],
          required: true,
        },
      ],
    });

    const formattedArbiters = arbiters.map((arbiter) => {
      const arbiterData = arbiter.toJSON();
      if (arbiter.user) {
        return { ...arbiterData, ...arbiter.user.toJSON() };
      }
      return arbiterData;
    });
    const response = {
      arbiters: formattedArbiters,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getSingleArbiter = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid arbiter ID",
      });
    }

    const arbiter = await User.findOne({
      where: { cbid: id },
      attributes: ["cbid", "name"],
      include: [
        {
          model: ArbiterDetails,
          attributes: [
            "title",
            "fideId",
            "aicfId",
            "profileUrl",
            "alternateContact",
            "officialId",
            "country",
            "state",
            "district",
            "city",
          ],
          required: true,
        },
      ],
    });
    if (!arbiter) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiter,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const getArbiterProfile = async (req, res) => {
  const userId = req.user.userId;
  if (!userId) {
    return sendResponse(res, 403, {
      success: false,
      error: "forbidden",
    });
  }
  try {
    const arbiter = await ArbiterDetails.findOne({
      where: { userId: userId },
      attributes: [
        "title",
        "fideId",
        "aicfId",
   
        "alternateContact",
        "officialId",
    
        "profileUrl",
        "country",
        "state",
        "district",
        "city",
        "pincode",
        "id",
      ],
      include: [
        {
          model: User,
          as: "user",
          attributes: ["name", "email", "phoneNumber", "cbid"],
          required: true,
        },
      ],
    });
    if (!arbiter) {
      return sendResponse(res, 204, {
        success: false,
        error: "Arbiter detail not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiter,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const createArbiterProfile = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, error } = arbiterDetailSchema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }
    const arbiterData = data;
    const cbid = await generateArbiterCbid();

    await User.update({ cbid }, { where: { id: userId } });
    const profileUrl = req?.file ? req?.file?.location : null;
    const newArbiter = await ArbiterDetails.create(
      { userId, profileUrl, ...arbiterData },
      {
        include: [
          {
            model: User,
            as: "user",
          },
        ],
      }
    );
    sendResponse(res, 201, {
      success: true,
      data: newArbiter,
    });
  } catch (error) {
    console.error("Error creating player:", error);
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};
/**
 * Edit a player profile * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object */
const editArbiterProfile = async (req, res) => {
  const userId = req.user.userId;
  try {
    const { success, data, error } = updateDetailsSchema
      .partial()
      .safeParse(req.body);
    const { otp } = req.body;
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }

    const playerData = data;
    
    const { name, phoneNumber, phoneChanged } = playerData;

    const update = {};
    if (phoneChanged) {
      const existingOtp = await Otp.findOne({
        where: { phoneNumber, type: "verification", platform: "sms" },
      });

      if (!existingOtp) {
        return sendResponse(res, 404, {
          success: false,
          error: { message: "OTP not found. Please request a new one." },
        });
      }
      const time = new Date(existingOtp.expiresAt);
      if (time < new Date()) {
        return sendResponse(res, 400, {
          success: false,
          error: { message: "OTP has expired. Please request a new one." },
        });
      }

      if (existingOtp.otp !== otp) {
        return sendResponse(res, 401, {
          success: false,
          error: { message: "Incorrect OTP." },
        });
      }
      await existingOtp.destroy();
      update.phoneNumber = phoneNumber;
    }
    if (name) {
      update.name = name;
    }

    if (name || phoneChanged) {
      await User.update(update, { where: { id: userId } });
    }

    const existingPlayer = await ArbiterDetails.findOne({
      where: { userId: userId },
    });
    if (!existingPlayer) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }

    const profileUrl = req?.file
      ? req?.file?.location
      : existingPlayer.profileUrl || null;

    const [count, rows] = await ArbiterDetails.update(
      { profileUrl, ...playerData },
      {
        where: { userId: userId },
        returning: true,
      }
    );
    if (existingPlayer?.profileUrl) {
      deleteFromS3(existingPlayer?.profileUrl);
    }
    if (count === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "arbiter not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: rows[0],
    });
  } catch (error) {
    deleteFromS3(req?.file?.location);
    handleError(res, error);
  }
};

const getArbiterTournaments = async (req, res) => {
  try {
    const UserId = req.user.userId;
    const { page = 1, limit = 3, title, status } = req.query;
    const offset = (page - 1) * limit;

    // Fix the variable name
    const whereClause = {
      tournamentStatus: { [Op.ne]: "archived" },
      arbiterId: UserId,
    };

    const searchTitle = title?.trim().toLowerCase().replace(/\s+/g, "-");
    if (title) whereClause.title = { [Op.iLike]: `%${searchTitle}%` };
    if (status) {
      if (status === "upcoming") {
        whereClause.startDate = { [Op.gt]: new Date() };
      } else if (status === "completed") {
        whereClause.endDate = { [Op.lt]: new Date() };
      } else if (status === "in-progress") {
        whereClause.startDate = { [Op.lte]: new Date() };
        whereClause.endDate = { [Op.gte]: new Date() };
      } else {
        whereClause.tournamentStatus = status;
      }
    }

    const { rows: Tournaments, count: total } =
      await Tournament.findAndCountAll({
        where: whereClause,
        attributes: [
          "title",
          "startDate",
          "endDate",
          "totalCashPrizeCurrency",
          "totalCashPrizeAmount",
          "registrationEndDate",
          "city",
        ],

        offset,
        limit,
      });
    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          tournaments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments: Tournaments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getArbiterForTournament = async (req, res) => {
  const { arbiterId } = req.query;
  if (!arbiterId || arbiterId.trim() === "") {
    return sendResponse(res, 422, {
      success: false,
      error: { message: "please enter the arbiter name or id" },
    });
  }
  try {
    const searchValue = arbiterId.trim();

    // Set up where clauses based on search term format
    const isCbId = searchValue.toLowerCase().startsWith("cb");

    // ArbiterDetails where clause (used when searching by various IDs)
    const detailsWhereClause = {};

    // User where clause (always includes role=arbiter)
    const userWhereClause = { role: "arbiter" };

    // If searchTerm starts with "cb", search in user table for CBID or name
    if (isCbId) {
      userWhereClause[Op.or] = [
        { cbid: { [Op.iLike]: `%${searchValue}%` } },
        { name: { [Op.iLike]: `%${searchValue}%` } },
      ];
    }
    // Otherwise search in ArbiterDetails table for various IDs
    else {
      detailsWhereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${searchValue}%` } },
        { aicfId: { [Op.iLike]: `%${searchValue}%` } },
        { officialId: { [Op.iLike]: `%${searchValue}%` } },
      ];
    }

    // Execute the search query
    const arbiters = await User.findAll({
      where: userWhereClause,
      attributes: ["id", "name", "cbid",'email'], // Added cbid to return values
      limit: 10,
      include: [
        {
          model: ArbiterDetails,
          where:
            Object.keys(detailsWhereClause).length > 0
              ? detailsWhereClause
              : undefined,
          attributes: [
            "fideId",
            "aicfId",
            "officialId",
          ], // Return relevant ID fields
          required: !isCbId, // Only require ArbiterDetails if searching by non-cb IDs
        },
      ],
      order: [["name", "ASC"]], // Sort results alphabetically by name
    });
    if (!arbiters || arbiters.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiters,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getArbiterTournamentInvite = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const invite = await InviteRequest.findAll({
      where: {
        userId: userId,
        type: "arbiter-request",
        status: { [Op.not]: ["accept", "reject"] },
      },
    });
    if (!invite) {
      return sendResponse(res, 204, {
        success: false,
        error: "No invite found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: invite,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const updateTournamentInvite = async (req, res) => {
  try {
    const { action, tournamentId } = req.body;
    const userId = req.user.userId;

    // Validate required fields
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: { message: "Unauthorized" },
      });
    }

    if (!tournamentId) {
      return sendResponse(res, 422, {
        success: false,
        error: { message: "Tournament id is required" },
      });
    }

    if (!action || !["accept", "reject"].includes(action)) {
      return sendResponse(res, 422, {
        success: false,
        error: { message: "Valid action (accept or reject) is required" },
      });
    }

    // Find the invitation notification
    const invite = await InviteRequest.findOne({
      where: {
        userId: userId,
        type: "arbiter-request",
        [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.tournament.tournamentId"),
            tournamentId
          ),
        ],
      },
    });

    if (!invite) {
      return sendResponse(res, 201, {
        success: false,
        error: { message: "Club Remove your Request" },
      });
    }

    // Find the tournament
    const tournament = await Tournament.findOne({
      where: { id: tournamentId },
    });

    if (!tournament) {
      await invite.destroy(); // Clean up orphaned notification
      return sendResponse(res, 404, {
        success: false,
        error: { message: "Tournament not found" },
      });
    }

    // Process rejection
    if (action === "reject") {
      await invite.destroy();
      return sendResponse(res, 200, {
        success: true,
        data: { message: "Tournament invitation rejected successfully" },
      });
    }

    // Process acceptance
    if (action === "accept") {
      // Check if tournament already has an arbiter
      if (tournament.arbiterId) {
        return sendResponse(res, 409, {
          success: false,
          error: { message: "Tournament already has an arbiter assigned" },
        });
      }

      // Transaction to ensure both operations succeed or fail together
      const transaction = await sequelize.transaction();

      try {
        // Update tournament with arbiter
        tournament.arbiterId = userId;
        await tournament.save({ transaction });

        // Remove the invitation
        await invite.destroy({ transaction });

        // Commit transaction
        await transaction.commit();
        emailService.sendClubArbiterAcceptEmail({
          email: invite.metadata.club.clubEmail,
          subject: `${invite.metadata.arbiter.arbiterName} has accepted your arbiter invitation`,
          clubName: invite.metadata.club.clubName,
          arbiterName: invite.metadata.arbiter.arbiterName,
          tournamentName: tournament.title,
          tournamentUrl: `${config.frontend_url}/dashboard/tournament/${tournament.title}`,
        });

        return sendResponse(res, 200, {
          success: true,
          error: { message: "Tournament invitation accepted successfully" },
        });
      } catch (txError) {
        // Rollback in case of error
        await transaction.rollback();
        throw txError;
      }
    }
  } catch (error) {
    console.error("Error updating tournament invitation:", error);
    handleError(res, error);
  }
};

// const queueTournamentPairingNotifications = async (req, res) => {
//   try {
//     const { tournamentId, round } = req.body;
//     const creatorId = req.user.userId;

//     // Validation
//     if (!tournamentId) {
//       return res.status(400).json({
//         success: false,
//         message: "Tournament ID is required",
//       });
//     }

//     if (!creatorId) {
//       return res.status(401).json({
//         success: false,
//         message: "Authentication required",
//       });
//     }

//     // Get tournament info
//     const tournament = await Tournament.findOne(
//       { where: { title: tournamentId } },
//       {
//         attributes: ["id", "title", "city", "registrationStartDate"],
//       }
//     );

//     if (!tournament) {
//       return res.status(404).json({
//         success: false,
//         message: `Tournament ${tournamentId} not found`,
//       });
//     }

//     // Check tournament status if needed
//     if (tournament.registrationStartDate > new Date()) {
//       return res.status(400).json({
//         success: false,
//         message: `Cannot send notifications for tournament`,
//       });
//     }

//     // Build pairing query
//     const pairingWhere = { tournament_id: tournament.id };

//     // If round specified, only get pairings for that round
//     if (round !== undefined) {
//       pairingWhere.round_id = round;
//     }

//     // Get all pairings
//     const pairings = await Pairing.findAll({
//       where: pairingWhere,
//       attributes: [
//         "id",
//         "round_id",
//         "age_category",
//         "gender_category",
//         "white_player_name",
//         "black_player_name",
//         "board_no",
//       ],
//       order: [
//         ["round_id", "ASC"],
//         ["board_no", "ASC"],
//       ],
//     });

//     if (pairings.length === 0) {
//       return res.status(404).json({
//         success: false,
//         message: round
//           ? `No pairings found for tournament ${tournamentId}, round ${round}`
//           : `No pairings found for tournament ${tournamentId}`,
//       });
//     }

//     // Extract all player names
//     const allPlayerNames = [
//       ...pairings.map((p) => p.white_player_name),
//       ...pairings.map((p) => p.black_player_name),
//     ].filter(Boolean);

//     // Get all users in a single query
//     const users = await User.findAll({
//       where: {
//         name: {
//           [Op.in]: allPlayerNames,
//         },
//       },
//       attributes: ["id", "name", "phone", "email"],
//     });

//     // Create a map for quick lookups by name
//     const userMap = new Map();
//     users.forEach((user) => {
//       userMap.set(user.name, user);
//     });

//     // Generate a batch ID for this group of notifications
//     const batchId = uuidv4();

//     // Prepare notification records
//     const notifications = [];
//     const detailsBaseUrl = `${
//       process.env.FRONTEND_URL || "https://chessbrigade.com"
//     }/tournaments/${tournamentId}/pairing-details/`;

//     // Format SMS template - can be moved to a template system later
//     const templateId =
//       process.env.SMS_PAIRING_TEMPLATE_ID || "tournament-pairing-default";

//     for (const pairing of pairings) {
//       const whiteUser = userMap.get(pairing.white_player_name);
//       const blackUser = userMap.get(pairing.black_player_name);

//       const detailsLink = `${detailsBaseUrl}${pairing.id}`;

//       // Prepare content template as JSONB
//       const notificationContent = {
//         tournamentName: tournament.name,
//         tournamentId: tournament.id,
//         roundNo: pairing.round,
//         boardNo: pairing.board_number || "N/A",
//         detailsLink: detailsLink,
//         pairingId: pairing.id,
//         ageCategory: pairing.age_category,
//         genderCategory: pairing.gender_category,
//         location: tournament.city,
//       };

//       // For white player
//       if (whiteUser) {
//         // Add opponent info to content
//         const whitePlayerContent = {
//           ...notificationContent,
//           playerName: whiteUser.name,
//           playerColor: "white",
//           opponentName: pairing.black_player_name,
//         };

//         // Create SMS notification if phone available
//         if (whiteUser.phone) {
//           notifications.push({
//             userId: whiteUser.id,
//             creatorId: creatorId,
//             phoneNumber: whiteUser.phone,
//             type: "tournament-pairing",
//             platform: "sms",
//             templateId: templateId,
//             content: whitePlayerContent,
//             status: "pending",
//             priority: 2, // Higher priority for tournament notifications
//             expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours expiry
//             batchId: batchId,
//             metadata: {
//               tournamentId,
//               pairingId: pairing.id,
//               round: pairing.round,
//             },
//           });
//         }
//       }

//       // For black player
//       if (blackUser) {
//         // Add opponent info to content
//         const blackPlayerContent = {
//           ...notificationContent,
//           playerName: blackUser.name,
//           playerColor: "black",
//           opponentName: pairing.white_player_name,
//         };

//         // Create SMS notification if phone available
//         if (blackUser.phone) {
//           notifications.push({
//             userId: blackUser.id,
//             creatorId: creatorId,
//             phoneNumber: blackUser.phone,
//             type: "tournament-pairing",
//             platform: "sms",
//             templateId: templateId,
//             content: blackPlayerContent,
//             status: "pending",
//             priority: 2, // Higher priority for tournament notifications
//             expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours expiry
//             batchId: batchId,
//             metadata: {
//               tournamentId,
//               pairingId: pairing.id,
//               round: pairing.round,
//             },
//           });
//         }
//       }
//     }

//     // Save all notifications in bulk
//     if (notifications.length > 0) {
//       await Notification.bulkCreate(notifications);
//     }

//     // Count notifications by round
//     const notificationsByRound = {};
//     notifications.forEach((notification) => {
//       const round = notification.metadata.round;
//       notificationsByRound[round] = (notificationsByRound[round] || 0) + 1;
//     });

//     // Return success response
//     return res.status(200).json({
//       success: true,
//       message: `Successfully queued ${notifications.length} pairing notifications`,
//       data: {
//         batchId: batchId,
//         tournamentId: tournamentId,
//         tournamentName: tournament.name,
//         totalNotifications: notifications.length,
//         notificationsByRound: notificationsByRound,
//         playerCount: new Set([...notifications.map((n) => n.userId)]).size,
//         missingPhoneCount:
//           allPlayerNames.length - users.filter((u) => u.phone).length,
//       },
//     });
//   } catch (error) {
//     console.error("Error queueing pairing notifications:", error);
//     return res.status(500).json({
//       success: false,
//       message: "Internal server error while queueing notifications",
//       error: process.env.NODE_ENV === "development" ? error.message : undefined,
//     });
//   }
// };

const tournamentPairingNotificationsSms = async (req, res) => {
  try {
    const { tournamentId, round } = req.body;
    const creatorId = req.user.userId;

    // Validation
    if (!tournamentId) {
      return res.status(400).json({
        success: false,
        message: "Tournament ID is required",
      });
    }

    if (!creatorId) {
      return res.status(401).json({
        success: false,
        message: "Authentication required",
      });
    }

    // Get tournament info
    const tournament = await Tournament.findOne(
      { where: { title: tournamentId } },
      {
        attributes: ["id", "title", "city", "registrationStartDate"],
      }
    );

    if (!tournament) {
      return res.status(404).json({
        success: false,
        message: `Tournament ${tournamentId} not found`,
      });
    }

    // Check tournament status if needed
    if (tournament.registrationStartDate > new Date()) {
      return res.status(400).json({
        success: false,
        message: `Cannot send notifications for tournament`,
      });
    }

    // Create notifications using the notification service
    const notifications = await notificationService.createPairingNotifications(
      tournament.id,
      round,
      creatorId
    );

    // Process notifications immediately
     cronService.runJobNow("process-sms-notifications");

    // Count notifications by round
    const notificationsByRound = {};
    notifications.forEach((notification) => {
      const round = notification.metadata.round_id;
      notificationsByRound[round] = (notificationsByRound[round] || 0) + 1;
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Successfully queued and processed ${notifications.length} pairing notifications`,
      data: {
        tournamentId: tournamentId,
        tournamentTitle: tournament.title,
        totalNotifications: notifications.length,
        notificationsByRound: notificationsByRound,
        playerCount: new Set([...notifications.map((n) => n.userId)]).size,
      },
    });
  } catch (error) {
    console.error("Error queueing pairing notifications:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while queueing notifications",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

const reportGenerate = async (req, res) => {
  try {
    const { type } = req.query;
    const { data, success, error } = getAllArbiterSchema.safeParse(req.query);
    const { rows: details } = await ArbiterDetails.findAndCountAll({
      attributes: {
        exclude: ["createdAt", "updatedAt", "id", "profileUrl", "userId"],
      },
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    // return;
    const plainDetails = details.map((arbiter) => {
      const plain = arbiter.toJSON();
      const flattened = {
        ...plain,
        userName: plain.user?.name || "",
        userEmail: plain.user?.email || "",
        userPhone: plain.user?.phoneNumber || "",
      };
      delete flattened.user;
      return flattened;
    });



    // return;

    // const newArray = Array.isArray(details)?details:[details]
    let result;
    if (type !== "pdf") {
      result = await exportToExcel({
        data: plainDetails,
        sheetName: "Arbiter_details",
        title: "Arbiter Details Report",
        reportType: "Registered Arbiters",
      });

      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=arbiter_details_report.xlsx"
      );
    } else {
      result = await exportToPDF({
        data: plainDetails,
        title: "Arbiter Details Report",
      });
      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.set({
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename=Arbiter_Details.pdf`,
      });
    }

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};
const removeProfileImage = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const arbiterDetail = await ArbiterDetails.findOne({
      where: { userId: userId },
    });
    if (!arbiterDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }
    if (arbiterDetail.profileUrl) {
      deleteFromS3(arbiterDetail.profileUrl);
    }
    arbiterDetail.profileUrl = null;
    await arbiterDetail.save();
    return sendResponse(res, 200, {
      success: true,
      message: "Profile image removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  getAllArbiter,
  getSingleArbiter,
  getArbiterProfile,
  createArbiterProfile,
  editArbiterProfile,
  getArbiterTournaments,
  getArbiterForTournament,
  getArbiterTournamentInvite,
  updateTournamentInvite,
  tournamentPairingNotificationsSms,
  reportGenerate,
  removeProfileImage,
};

// In your player profile update controller
const generateArbiterCbid = async () => {
  try {
    // Generate the formatted CBID
    const prefix = "CB";
    const currentYear = new Date().getFullYear();
    const yearCode = currentYear.toString().slice(-2);
    const arbiterCode = "AR";

    const yearPattern = `${prefix}${yearCode}${arbiterCode}`;
    const latestUser = await User.findOne({
      where: {
        cbid: {
          [Op.iLike]: `${yearPattern}%`,
        },
      },
      order: [["cbid", "DESC"]],
    });
    // Format sequence number with leading zeros - starts from 1 each year
    let nextSequence = 1;
    if (latestUser && latestUser.cbid) {
      const match = latestUser.cbid.match(/(\d{5})$/);
      if (match) {
        nextSequence = parseInt(match[1], 10) + 1;
      }
    }

    const sequenceNumber = nextSequence.toString().padStart(5, "0");
    return `${prefix}${yearCode}${arbiterCode}${sequenceNumber}`;
  } catch (error) {
    console.error("Error generating CBID:", error);
    throw error;
  }
};
