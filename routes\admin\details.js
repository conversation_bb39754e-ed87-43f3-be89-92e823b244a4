const express = require("express");
const router = express.Router({ mergeParams: true });
const { getSinglePlayer, 
    getClubDetailById, 
    getSingleArbiter, 
    getPayment, 
    getAllTournament,
    getClubEarnings, 
    updatePlayerProfile,
    updateClubProfile,
    updateArbiterProfile,
    } = require("../../controllers/admin/DetailsController");
const { uploadFactory, handleUploadError } = require("../../utils/s3");

router.get('/single/:id',getSinglePlayer)
router.get('/single/club/:id',getClubDetailById)
router.get('/single/arbiter/:id',getSingleArbiter)
router.get('/payment',getPayment)
router.get('/payment/tournament',getAllTournament)
router.get('/tournament/payment',getClubEarnings)
router.put('/update/player/:id',uploadFactory.player.profileImage(),handleUploadError,updatePlayerProfile)
router.put('/update/club/:id', uploadFactory.club.profileImage(),handleUploadError,updateClubProfile)
router.put('/update/arbiter/:id', uploadFactory.arbiter.profileImage(),handleUploadError,updateArbiterProfile)


module.exports = router;