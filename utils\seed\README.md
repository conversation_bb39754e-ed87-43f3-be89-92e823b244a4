# Database Seeding Scripts

This directory contains scripts for seeding the database with sample data for development and load testing.

## Files Overview

### Core Seeding
- `seed.js` - Main seeding script for development data
- `user.js` - User data generation for development
- `runSeed.js` - <PERSON>ript to run development seeding

### Load Testing
- `loadTestSeed.js` - Comprehensive load testing data generation
- `runLoadTestSeed.js` - <PERSON><PERSON><PERSON> to run load testing seeding
- `cleanupLoadTestData.js` - <PERSON><PERSON><PERSON> to clean up load testing data

## Load Testing Data

The load testing seed creates a comprehensive dataset for performance testing:

### Data Volume
- **500 Players** - With complete player details and club associations
- **100 Clubs** - With full club details and banking information
- **100 Arbiters** - With arbiter details and qualifications
- **200 Tournaments** - Active tournaments with various configurations
- **500 Registrations** - Mix of individual and bulk registrations
- **All Payments** - Complete payment records with mock Razorpay data

### Registration Distribution
- **350 Individual Registrations** (70%) - Direct player registrations
- **150 Bulk Registrations** (30%) - Club-based bulk registrations (5 players each)

### Payment Data
All registrations include realistic payment data with:
- Mock Ra<PERSON>pay order IDs, payment IDs, and signatures
- Realistic fee calculations (2.4% + 18% tax)
- Various payment methods (UPI, card, netbanking)
- Complete payment response JSON matching production format

## Usage

### Running Load Test Seeding

```bash
# Create load testing data
node utils/seed/runLoadTestSeed.js
```

This will create:
- 701 total users (500 players + 100 clubs + 100 arbiters + 1 admin)
- 200 tournaments across all clubs
- 500 player registrations with payments
- All associated details and relationships

### Cleaning Up Load Test Data

```bash
# Remove all load testing data
node utils/seed/cleanupLoadTestData.js
```

This safely removes all load test data in the correct order to handle foreign key constraints.

### Running Development Seeding

```bash
# Create development data (smaller dataset)
node utils/seed/runSeed.js
```

## Data Patterns

### User Identification
Load test data uses specific patterns for easy identification:
- **Emails**: `loadtest.{role}{number}@chessbrigade.com`
- **CBIDs**: 
  - Players: `LTCB25US{5-digit-number}`
  - Clubs: `LTC{3-digit-number}`
  - Arbiters: `LTA{3-digit-number}`
- **Phone Numbers**: `9{role-code}{8-digit-number}`

### Tournament Naming
- **Pattern**: `loadtest-{club-name}-championship-{1-2}`
- **Status**: All tournaments are set to "active" for testing registrations

### Payment IDs
- **Transaction IDs**: `LT-{8-character-random}`
- **Razorpay Order IDs**: `order_LT{14-character-random}`
- **Razorpay Payment IDs**: `pay_LT{14-character-random}`

## Database Relationships

The load test data maintains all proper relationships:
- Players are assigned to clubs randomly
- Tournaments are created by clubs with assigned arbiters
- Registrations link players to tournaments
- Payments link to either individual registrations or bulk registrations
- Bulk registrations contain player lists and link to club details

## Performance Considerations

### Bulk Operations
All data creation uses bulk insert operations for optimal performance:
- Users: Single bulk insert of 701 records
- Details: Bulk inserts for club, player, and arbiter details
- Tournaments: Bulk insert of 200 tournaments
- Registrations: Bulk insert of individual and bulk registrations
- Payments: Bulk insert of all payment records

### Memory Usage
The scripts are designed to be memory efficient:
- Data is generated in batches
- No large arrays kept in memory unnecessarily
- Proper cleanup of temporary variables

## Error Handling

### Duplicate Prevention
- Checks for existing data before creation
- Uses `ignoreDuplicates: true` for bulk operations
- Filters out existing users by email and CBID

### Graceful Cleanup
- Proper foreign key constraint handling
- Deletion in correct order (payments → registrations → tournaments → details → users)
- Transaction rollback on errors

## Monitoring

Both scripts provide detailed progress logging:
- Creation counts for each data type
- Timing information
- Summary statistics
- Error reporting with stack traces

## Use Cases

### Load Testing
Perfect for testing:
- Tournament registration performance
- Payment processing under load
- Search and filtering with large datasets
- Report generation with substantial data
- API response times with realistic data volumes

### Development
Smaller development dataset for:
- Feature development
- UI testing
- Integration testing
- Demo purposes

## Notes

- All passwords are hashed using bcrypt with salt rounds of 10
- Default password for all users is "password123"
- All dates are generated relative to current date for realistic testing
- Payment amounts and fees are calculated realistically
- Geographic data uses real Indian states and cities
- Club and player assignments are randomized for realistic distribution
